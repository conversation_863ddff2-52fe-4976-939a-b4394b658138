package com.anytech.anytxn.parameter.card.service;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.base.card.domain.dto.MarkupFeeReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.MarkupFeeResDTO;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmMarkUpFee;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmMarkupFeeMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmMarkupFeeSelfMapper;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.StringUtils;
import java.math.BigDecimal;
import java.util.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;

@ExtendWith(MockitoExtension.class)
class MarkUpFeeServiceTest {
    @InjectMocks
    private MarkupFeeServiceImpl service;
    @Mock
    private ParmMarkupFeeMapper markUpFeeMapper;
    @Mock
    private ParmMarkupFeeSelfMapper markUpFeeSelfMapper;
    @Mock
    private Number16IdGen numberIdGenerator;
    private MockedStatic<BeanMapping> beanMappingMockedStatic;
    private MockedStatic<OrgNumberUtils> orgNumberUtilsMockedStatic;
    private MockedStatic<TenantUtils> tenantUtilsMockedStatic;
    private MockedStatic<PageHelper> pageHelperMockedStatic;
    private MockedStatic<StringUtils> stringUtilsMockedStatic;

    @BeforeEach
    void setUp() {
        beanMappingMockedStatic = Mockito.mockStatic(BeanMapping.class);
        orgNumberUtilsMockedStatic = Mockito.mockStatic(OrgNumberUtils.class);
        tenantUtilsMockedStatic = Mockito.mockStatic(TenantUtils.class);
        pageHelperMockedStatic = Mockito.mockStatic(PageHelper.class);
        stringUtilsMockedStatic = Mockito.mockStatic(StringUtils.class);
    }

    @AfterEach
    void tearDown() {
        beanMappingMockedStatic.close();
        orgNumberUtilsMockedStatic.close();
        tenantUtilsMockedStatic.close();
        pageHelperMockedStatic.close();
        stringUtilsMockedStatic.close();
    }

    // findByOrgAndTableId - happy path
    @Test
    void testFindByOrgAndTableId_HappyPath() {
        ParmMarkUpFee entity = new ParmMarkUpFee();
        Mockito.lenient().when(markUpFeeSelfMapper.selectByOrgAndTableId(anyString(), anyString())).thenReturn(entity);
        MarkupFeeResDTO dto = new MarkupFeeResDTO();
        beanMappingMockedStatic.when(() -> BeanMapping.copy(any(), eq(MarkupFeeResDTO.class))).thenReturn(dto);
        MarkupFeeResDTO result = service.findByOrgAndTableId("org", "tid");
        assertNotNull(result);
        assertEquals(dto, result);
    }

    // findByOrgAndTableId - not found
    @Test
    void testFindByOrgAndTableId_NotFound() {
        Mockito.when(markUpFeeSelfMapper.selectByOrgAndTableId(anyString(), anyString())).thenReturn(null);
        AnyTxnParameterException ex = assertThrows(AnyTxnParameterException.class, () -> service.findByOrgAndTableId("org", "tid"));
        assertEquals(AnyTxnParameterRespCodeEnum.S_GET_PARM_MARK_UP_FEE_FAULT.getCode(), ex.getErrCode());
    }

    // add - happy path
    @Test
    void testAdd_HappyPath() {
        MarkupFeeReqDTO req = new MarkupFeeReqDTO();
        req.setOrganizationNumber("org");
        req.setTableId("tid");
        Mockito.lenient().when(markUpFeeSelfMapper.isExists(anyString(), anyString())).thenReturn(0);
        ParmMarkUpFee entity = new ParmMarkUpFee();
        beanMappingMockedStatic.when(() -> BeanMapping.copy(any(), eq(ParmMarkUpFee.class))).thenReturn(entity);
        tenantUtilsMockedStatic.when(TenantUtils::getTenantId).thenReturn("tenant");
        Mockito.lenient().when(numberIdGenerator.generateId(anyString())).thenReturn(123L);
        ParameterCompare compare = ParameterCompare.getBuilder().withAfter(entity).build(ParmMarkUpFee.class);
        // 由于ParameterCompare.getBuilder().withAfter().build()是链式静态工厂，直接用真实对象
        ParameterCompare result = service.add(req);
        assertNotNull(result);
        assertEquals(compare.getAfter(), result.getAfter());
    }

    // add - 参数为空
    @Test
    void testAdd_ParamNull() {
        AnyTxnParameterException ex = assertThrows(AnyTxnParameterException.class, () -> service.add(null));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), ex.getErrCode());
    }

    // add - 机构号为空
    @Test
    void testAdd_OrgNull() {
        MarkupFeeReqDTO req = new MarkupFeeReqDTO();
        req.setTableId("tid");
        orgNumberUtilsMockedStatic.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
        Mockito.lenient().when(markUpFeeSelfMapper.isExists(anyString(), anyString())).thenReturn(0);
        ParmMarkUpFee entity = new ParmMarkUpFee();
        beanMappingMockedStatic.when(() -> BeanMapping.copy(any(), eq(ParmMarkUpFee.class))).thenReturn(entity);
        tenantUtilsMockedStatic.when(TenantUtils::getTenantId).thenReturn("tenant");
        Mockito.lenient().when(numberIdGenerator.generateId(anyString())).thenReturn(123L);
        req.setOrganizationNumber(null);
        ParameterCompare result = service.add(req);
        assertNotNull(result);
    }

    // add - 已存在
    @Test
    void testAdd_Exist() {
        MarkupFeeReqDTO req = new MarkupFeeReqDTO();
        req.setOrganizationNumber("org");
        req.setTableId("tid");
        Mockito.when(markUpFeeSelfMapper.isExists(anyString(), anyString())).thenReturn(1);
        AnyTxnParameterException ex = assertThrows(AnyTxnParameterException.class, () -> service.add(req));
        assertEquals(AnyTxnParameterRespCodeEnum.S_EXIST_PARM_MARK_UP_FEE_FAULT.getCode(), ex.getErrCode());
    }

    // removeById - happy path
    @Test
    void testRemoveById_HappyPath() {
        ParmMarkUpFee entity = new ParmMarkUpFee();
        Mockito.lenient().when(markUpFeeMapper.selectByPrimaryKey(anyString())).thenReturn(entity);
        ParameterCompare compare = ParameterCompare.getBuilder().withBefore(entity).build(ParmMarkUpFee.class);
        ParameterCompare result = service.removeById("id");
        assertNotNull(result);
        assertEquals(compare.getBefore(), result.getBefore());
    }

    // removeById - id为空
    @Test
    void testRemoveById_IdNull() {
        AnyTxnParameterException ex = assertThrows(AnyTxnParameterException.class, () -> service.removeById(null));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), ex.getErrCode());
    }

    // removeById - not found
    @Test
    void testRemoveById_NotFound() {
        Mockito.lenient().when(markUpFeeMapper.selectByPrimaryKey(anyString())).thenReturn(null);
        AnyTxnParameterException ex = assertThrows(AnyTxnParameterException.class, () -> service.removeById("id"));
        assertEquals(AnyTxnParameterRespCodeEnum.S_GET_PARM_MARK_UP_FEE_BY_ID_FAULT.getCode(), ex.getErrCode());
    }

    // removeByOrgAndTableId - happy path
    @Test
    void testRemoveByOrgAndTableId_HappyPath() {
        ParmMarkUpFee entity = new ParmMarkUpFee();
        Mockito.lenient().when(markUpFeeSelfMapper.selectByOrgAndTableId(anyString(), anyString())).thenReturn(entity);
        ParameterCompare compare = ParameterCompare.getBuilder().withBefore(entity).build(ParmMarkUpFee.class);
        ParameterCompare result = service.removeByOrgAndTableId("org", "tid");
        assertNotNull(result);
        assertEquals(compare.getBefore(), result.getBefore());
    }

    // removeByOrgAndTableId - 参数为空
    @Test
    void testRemoveByOrgAndTableId_ParamNull() {
        stringUtilsMockedStatic.when(() -> StringUtils.isEmpty(any())).thenReturn(true);
        AnyTxnParameterException ex = assertThrows(AnyTxnParameterException.class, () -> service.removeByOrgAndTableId(null, null));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), ex.getErrCode());
    }

    // removeByOrgAndTableId - not found
    @Test
    void testRemoveByOrgAndTableId_NotFound() {
        stringUtilsMockedStatic.when(() -> StringUtils.isEmpty(any())).thenReturn(false);
        Mockito.lenient().when(markUpFeeSelfMapper.selectByOrgAndTableId(anyString(), anyString())).thenReturn(null);
        AnyTxnParameterException ex = assertThrows(AnyTxnParameterException.class, () -> service.removeByOrgAndTableId("org", "tid"));
        assertEquals(AnyTxnParameterRespCodeEnum.S_GET_PARM_MARK_UP_FEE_BY_ID_FAULT.getCode(), ex.getErrCode());
    }

    // modify - happy path
    @Test
    void testModify_HappyPath() {
        MarkupFeeReqDTO req = new MarkupFeeReqDTO();
        req.setId("id");
        req.setOrganizationNumber("org");
        req.setTableId("tid");
        ParmMarkUpFee oldEntity = new ParmMarkUpFee();
        oldEntity.setOrganizationNumber("org");
        oldEntity.setTableId("tid");
        Mockito.lenient().when(markUpFeeMapper.selectByPrimaryKey(anyString())).thenReturn(oldEntity);
        ParmMarkUpFee newEntity = new ParmMarkUpFee();
        beanMappingMockedStatic.when(() -> BeanMapping.copy(any(), eq(ParmMarkUpFee.class))).thenReturn(newEntity);
        ParameterCompare compare = ParameterCompare.getBuilder().withAfter(newEntity).withBefore(oldEntity).build(ParmMarkUpFee.class);
        ParameterCompare result = service.modify(req);
        assertNotNull(result);
        assertEquals(compare.getAfter(), result.getAfter());
        assertEquals(compare.getBefore(), result.getBefore());
    }

    // modify - 参数为空
    @Test
    void testModify_ParamNull() {
        AnyTxnParameterException ex = assertThrows(AnyTxnParameterException.class, () -> service.modify(null));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), ex.getErrCode());
    }

    // modify - 机构号或tableId变更且已存在
    @Test
    void testModify_OrgOrTableIdChangedAndExist() {
        MarkupFeeReqDTO req = new MarkupFeeReqDTO();
        req.setId("id");
        req.setOrganizationNumber("org2");
        req.setTableId("tid2");
        ParmMarkUpFee oldEntity = new ParmMarkUpFee();
        oldEntity.setOrganizationNumber("org1");
        oldEntity.setTableId("tid1");
        Mockito.lenient().when(markUpFeeMapper.selectByPrimaryKey(anyString())).thenReturn(oldEntity);
        Mockito.lenient().when(markUpFeeSelfMapper.isExists(anyString(), anyString())).thenReturn(1);
        AnyTxnParameterException ex = assertThrows(AnyTxnParameterException.class, () -> service.modify(req));
        assertEquals(AnyTxnParameterRespCodeEnum.S_EXIST_PARM_MARK_UP_FEE_FAULT.getCode(), ex.getErrCode());
    }

    // findById - happy path
    @Test
    void testFindById_HappyPath() {
        ParmMarkUpFee entity = new ParmMarkUpFee();
        Mockito.lenient().when(markUpFeeMapper.selectByPrimaryKey(anyString())).thenReturn(entity);
        MarkupFeeResDTO dto = new MarkupFeeResDTO();
        beanMappingMockedStatic.when(() -> BeanMapping.copy(any(), eq(MarkupFeeResDTO.class))).thenReturn(dto);
        MarkupFeeResDTO result = service.findById("id");
        assertNotNull(result);
        assertEquals(dto, result);
    }

    // findById - id为空
    @Test
    void testFindById_IdNull() {
        AnyTxnParameterException ex = assertThrows(AnyTxnParameterException.class, () -> service.findById(null));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), ex.getErrCode());
    }

    // findById - not found
    @Test
    void testFindById_NotFound() {
        Mockito.lenient().when(markUpFeeMapper.selectByPrimaryKey(anyString())).thenReturn(null);
        AnyTxnParameterException ex = assertThrows(AnyTxnParameterException.class, () -> service.findById("id"));
        assertEquals(AnyTxnParameterRespCodeEnum.S_GET_PARM_MARK_UP_FEE_BY_ID_FAULT.getCode(), ex.getErrCode());
    }

    // findList - happy path
    @Test
    void testFindList_HappyPath() {
        Page<ParmMarkUpFee> page = Mockito.mock(Page.class);
        pageHelperMockedStatic.when(() -> PageHelper.startPage(anyInt(), anyInt())).thenReturn(page);
        stringUtilsMockedStatic.when(() -> StringUtils.isEmpty(any())).thenReturn(false);
        List<ParmMarkUpFee> entityList = Collections.singletonList(new ParmMarkUpFee());
        Mockito.lenient().when(markUpFeeSelfMapper.selectByCondition(anyString(), anyString(), anyString())).thenReturn(entityList);
        List<MarkupFeeResDTO> dtoList = Collections.singletonList(new MarkupFeeResDTO());
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(anyList(), eq(MarkupFeeResDTO.class))).thenReturn(dtoList);
        Mockito.lenient().when(page.getTotal()).thenReturn(1L);
        Mockito.lenient().when(page.getPages()).thenReturn(1);
        PageResultDTO<MarkupFeeResDTO> result = service.findList(1, 10, "tid", "desc", "org");
        assertNotNull(result);
        assertEquals(dtoList, result.getData());
    }
} 