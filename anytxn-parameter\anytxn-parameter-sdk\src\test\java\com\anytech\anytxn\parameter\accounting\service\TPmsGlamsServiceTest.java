package com.anytech.anytxn.parameter.accounting.service;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlamsDTO;
import com.anytech.anytxn.parameter.base.accounting.domain.model.TPmsGlams;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.deprecated.TPmsGlamsMapper;
import com.anytech.anytxn.parameter.common.mapper.deprecated.TPmsGlamsSelfMapper;

import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;


import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TPmsGlamsService Unit Test Class
 *
 * <AUTHOR>
 * @date 2019-10-09
 */
@ExtendWith(MockitoExtension.class)
class TPmsGlamsServiceTest {

    @Mock
    private TPmsGlamsSelfMapper tPmsGlamsSelfMapper;
    
    @Mock
    private TPmsGlamsMapper tPmsGlamsMapper;
    
    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private TPmsGlamsServiceImpl tPmsGlamsService;

    private MockedStatic<BeanMapping> beanMappingMockedStatic;
    private MockedStatic<OrgNumberUtils> orgNumberUtilsMockedStatic;
    private MockedStatic<TenantUtils> tenantUtilsMockedStatic;
    private MockedStatic<PageHelper> pageHelperMockedStatic;

    @BeforeEach
    void setUp() {
        beanMappingMockedStatic = mockStatic(BeanMapping.class);
        orgNumberUtilsMockedStatic = mockStatic(OrgNumberUtils.class);
        tenantUtilsMockedStatic = mockStatic(TenantUtils.class);
        pageHelperMockedStatic = mockStatic(PageHelper.class);
    }

    @Test
    void testSelectByParams_Success() throws AnyTxnParameterException {
        // Arrange
        String organizationNumber = "0001";
        String branchid = "DINERS";
        String txnCode = "TXN001";
        String financeStatus = "A";
        String intInt = "I";
        String productNumber = "PROD001";
        String priceTaxInd = "Y";

        TPmsGlams tPmsGlams = new TPmsGlams();
        tPmsGlams.setId("1");
        tPmsGlams.setOrganizationNumber(organizationNumber);
        tPmsGlams.setBranchid(branchid);
        tPmsGlams.setTxnCode(txnCode);
        tPmsGlams.setFinanceStatus(financeStatus);
        tPmsGlams.setIntInt(intInt);
        tPmsGlams.setProductNumber(productNumber);
        tPmsGlams.setPriceTaxInd(priceTaxInd);
        tPmsGlams.setDrcr("D");
        tPmsGlams.setGlAcct("1001");
        tPmsGlams.setCreateTime(LocalDateTime.now());
        tPmsGlams.setUpdateTime(LocalDateTime.now());
        tPmsGlams.setUpdateBy("testUser");
        tPmsGlams.setVersionNumber(1L);

        List<TPmsGlams> pmsGlamsList = Collections.singletonList(tPmsGlams);

        TPmsGlamsDTO tPmsGlamsDTO = new TPmsGlamsDTO();
        tPmsGlamsDTO.setId("1");
        tPmsGlamsDTO.setOrganizationNumber(organizationNumber);
        tPmsGlamsDTO.setBranchid(branchid);
        tPmsGlamsDTO.setTxnCode(txnCode);
        tPmsGlamsDTO.setFinanceStatus(financeStatus);
        tPmsGlamsDTO.setIntInt(intInt);
        tPmsGlamsDTO.setProductNumber(productNumber);
        tPmsGlamsDTO.setPriceTaxInd(priceTaxInd);
        tPmsGlamsDTO.setDrcr("D");
        tPmsGlamsDTO.setGlAcct("1001");
        tPmsGlamsDTO.setCreateTime(LocalDateTime.now());
        tPmsGlamsDTO.setUpdateTime(LocalDateTime.now());
        tPmsGlamsDTO.setUpdateBy("testUser");
        tPmsGlamsDTO.setVersionNumber(1L);

        List<TPmsGlamsDTO> expectedDTOList = Collections.singletonList(tPmsGlamsDTO);

        Mockito.lenient().when(tPmsGlamsSelfMapper.selectByParams(organizationNumber, branchid, txnCode, 
                financeStatus, intInt, productNumber, priceTaxInd)).thenReturn(pmsGlamsList);
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(pmsGlamsList, TPmsGlamsDTO.class))
                .thenReturn(expectedDTOList);

        // Act
        List<TPmsGlamsDTO> result = tPmsGlamsService.selectByParams(organizationNumber, branchid, txnCode, 
                financeStatus, intInt, productNumber, priceTaxInd);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(tPmsGlamsDTO.getId(), result.get(0).getId());
        assertEquals(tPmsGlamsDTO.getOrganizationNumber(), result.get(0).getOrganizationNumber());
        assertEquals(tPmsGlamsDTO.getTxnCode(), result.get(0).getTxnCode());
        
        verify(tPmsGlamsSelfMapper).selectByParams(organizationNumber, branchid, txnCode, 
                financeStatus, intInt, productNumber, priceTaxInd);
    }

    @Test
    void testSelectByParams_EmptyList() throws AnyTxnParameterException {
        // Arrange
        String organizationNumber = "0001";
        String branchid = "DINERS";
        String txnCode = "TXN001";
        String financeStatus = "A";
        String intInt = "I";
        String productNumber = "PROD001";
        String priceTaxInd = "Y";

        List<TPmsGlams> emptyList = new ArrayList<>();

        Mockito.lenient().when(tPmsGlamsSelfMapper.selectByParams(organizationNumber, branchid, txnCode, 
                financeStatus, intInt, productNumber, priceTaxInd)).thenReturn(emptyList);

        // Act
        List<TPmsGlamsDTO> result = tPmsGlamsService.selectByParams(organizationNumber, branchid, txnCode, 
                financeStatus, intInt, productNumber, priceTaxInd);

        // Assert
        assertNull(result);
        
        verify(tPmsGlamsSelfMapper).selectByParams(organizationNumber, branchid, txnCode,
                financeStatus, intInt, productNumber, priceTaxInd);
    }

    @Test
    void testPage_Success() {
        // Arrange
        int page = 1;
        int rows = 10;
        String organizationNumber = "0001";



        TPmsGlams tPmsGlams = new TPmsGlams();
        tPmsGlams.setId("1");
        tPmsGlams.setOrganizationNumber(organizationNumber);
        tPmsGlams.setBranchid("DINERS");
        tPmsGlams.setTxnCode("TXN001");
        tPmsGlams.setFinanceStatus("A");
        tPmsGlams.setIntInt("I");
        tPmsGlams.setProductNumber("PROD001");
        tPmsGlams.setPriceTaxInd("Y");
        tPmsGlams.setDrcr("D");
        tPmsGlams.setGlAcct("1001");
        tPmsGlams.setCreateTime(LocalDateTime.now());
        tPmsGlams.setUpdateTime(LocalDateTime.now());
        tPmsGlams.setUpdateBy("testUser");
        tPmsGlams.setVersionNumber(1L);

        List<TPmsGlams> tPmsGlamsList = Collections.singletonList(tPmsGlams);

        // Skip PageHelper mock for now - let it use real implementation
        orgNumberUtilsMockedStatic.when(() -> OrgNumberUtils.getOrg()).thenReturn(organizationNumber);
        Mockito.lenient().when(tPmsGlamsSelfMapper.selectAll(false, organizationNumber)).thenReturn(tPmsGlamsList);

        // Act
        PageResultDTO<TPmsGlamsDTO> result = tPmsGlamsService.page(page, rows);

        // Assert
        assertNotNull(result);
        assertEquals(page, result.getPage());
        assertEquals(rows, result.getRows());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().size());

        verify(tPmsGlamsSelfMapper).selectAll(false, organizationNumber);
    }

    @Test
    void testPage_EmptyList() {
        // Arrange
        int page = 1;
        int rows = 10;
        String organizationNumber = "0001";



        List<TPmsGlams> emptyList = new ArrayList<>();

        // Skip PageHelper mock for now - let it use real implementation
        orgNumberUtilsMockedStatic.when(() -> OrgNumberUtils.getOrg()).thenReturn(organizationNumber);
        Mockito.lenient().when(tPmsGlamsSelfMapper.selectAll(false, organizationNumber)).thenReturn(emptyList);

        // Act
        PageResultDTO<TPmsGlamsDTO> result = tPmsGlamsService.page(page, rows);

        // Assert
        assertNotNull(result);
        assertEquals(page, result.getPage());
        assertEquals(rows, result.getRows());
        assertNull(result.getData());

        verify(tPmsGlamsSelfMapper).selectAll(false, organizationNumber);
    }

    @Test
    void testPage_Exception() {
        // Arrange
        int page = 1;
        int rows = 10;
        String organizationNumber = "0001";



        // Skip PageHelper mock for now - let it use real implementation
        orgNumberUtilsMockedStatic.when(() -> OrgNumberUtils.getOrg()).thenReturn(organizationNumber);
        Mockito.lenient().when(tPmsGlamsSelfMapper.selectAll(false, organizationNumber))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> tPmsGlamsService.page(page, rows));

        assertEquals(AnyTxnParameterRespCodeEnum.D_PAGE_TPMS_GLAMS_FAULT.getCode(), exception.getErrCode());

        verify(tPmsGlamsSelfMapper).selectAll(false, organizationNumber);
    }

    @Test
    void testDetail_Success() {
        // Arrange
        Long id = 1L;

        TPmsGlams tPmsGlams = new TPmsGlams();
        tPmsGlams.setId("1");
        tPmsGlams.setOrganizationNumber("0001");
        tPmsGlams.setBranchid("DINERS");
        tPmsGlams.setTxnCode("TXN001");
        tPmsGlams.setFinanceStatus("A");
        tPmsGlams.setIntInt("I");
        tPmsGlams.setProductNumber("PROD001");
        tPmsGlams.setPriceTaxInd("Y");
        tPmsGlams.setDrcr("D");
        tPmsGlams.setGlAcct("1001");
        tPmsGlams.setCreateTime(LocalDateTime.now());
        tPmsGlams.setUpdateTime(LocalDateTime.now());
        tPmsGlams.setUpdateBy("testUser");
        tPmsGlams.setVersionNumber(1L);

        Mockito.lenient().when(tPmsGlamsMapper.selectByPrimaryKey(id)).thenReturn(tPmsGlams);

        // Act
        TPmsGlamsDTO result = tPmsGlamsService.detail(id);

        // Assert
        assertNotNull(result);
        assertEquals(tPmsGlams.getId(), result.getId());
        assertEquals(tPmsGlams.getOrganizationNumber(), result.getOrganizationNumber());
        assertEquals(tPmsGlams.getTxnCode(), result.getTxnCode());

        verify(tPmsGlamsMapper).selectByPrimaryKey(id);
    }

    @Test
    void testDetail_NotFound() {
        // Arrange
        Long id = 1L;

        Mockito.lenient().when(tPmsGlamsMapper.selectByPrimaryKey(id)).thenReturn(null);

        // Act
        TPmsGlamsDTO result = tPmsGlamsService.detail(id);

        // Assert
        assertNull(result);

        verify(tPmsGlamsMapper).selectByPrimaryKey(id);
    }

    @Test
    void testRemove_Success() {
        // Arrange
        Long id = 1L;

        Mockito.lenient().when(tPmsGlamsMapper.deleteByPrimaryKey(id)).thenReturn(1);

        // Act
        boolean result = tPmsGlamsService.remove(id);

        // Assert
        assertTrue(result);

        verify(tPmsGlamsMapper).deleteByPrimaryKey(id);
    }

    @Test
    void testRemove_Failed() {
        // Arrange
        Long id = 1L;

        Mockito.lenient().when(tPmsGlamsMapper.deleteByPrimaryKey(id)).thenReturn(0);

        // Act
        boolean result = tPmsGlamsService.remove(id);

        // Assert
        assertFalse(result);

        verify(tPmsGlamsMapper).deleteByPrimaryKey(id);
    }

    @Test
    void testAdd_Success() {
        // Arrange
        TPmsGlamsDTO data = new TPmsGlamsDTO();
        data.setId("1");
        data.setOrganizationNumber("0001");
        data.setBranchid("DINERS");
        data.setTxnCode("TXN001");
        data.setFinanceStatus("A");
        data.setIntInt("I");
        data.setProductNumber("PROD001");
        data.setPriceTaxInd("Y");
        data.setDrcr("D");
        data.setGlAcct("1001");
        data.setCreateTime(LocalDateTime.now());
        data.setUpdateTime(LocalDateTime.now());
        data.setUpdateBy("testUser");
        data.setVersionNumber(1L);

        String tenantId = "tenant001";

        tenantUtilsMockedStatic.when(() -> TenantUtils.getTenantId()).thenReturn(tenantId);
        Mockito.lenient().when(numberIdGenerator.generateId(tenantId)).thenReturn(123456L);
        Mockito.lenient().when(tPmsGlamsSelfMapper.insert(any(TPmsGlams.class))).thenReturn(1);

        // Act
        boolean result = tPmsGlamsService.add(data);

        // Assert
        assertTrue(result);

        verify(numberIdGenerator).generateId(tenantId);
        verify(tPmsGlamsSelfMapper).insert(any(TPmsGlams.class));
    }

    @Test
    void testAdd_NullData() {
        // Arrange
        TPmsGlamsDTO data = null;

        // Act
        boolean result = tPmsGlamsService.add(data);

        // Assert
        assertFalse(result);

        verify(numberIdGenerator, never()).generateId(anyString());
        verify(tPmsGlamsSelfMapper, never()).insert(any(TPmsGlams.class));
    }

    @Test
    void testAdd_InsertFailed() {
        // Arrange
        TPmsGlamsDTO data = new TPmsGlamsDTO();
        data.setId("1");
        data.setOrganizationNumber("0001");
        data.setBranchid("DINERS");
        data.setTxnCode("TXN001");
        data.setFinanceStatus("A");
        data.setIntInt("I");
        data.setProductNumber("PROD001");
        data.setPriceTaxInd("Y");
        data.setDrcr("D");
        data.setGlAcct("1001");
        data.setCreateTime(LocalDateTime.now());
        data.setUpdateTime(LocalDateTime.now());
        data.setUpdateBy("testUser");
        data.setVersionNumber(1L);

        String tenantId = "tenant001";

        tenantUtilsMockedStatic.when(() -> TenantUtils.getTenantId()).thenReturn(tenantId);
        Mockito.lenient().when(numberIdGenerator.generateId(tenantId)).thenReturn(123456L);
        Mockito.lenient().when(tPmsGlamsSelfMapper.insert(any(TPmsGlams.class))).thenReturn(0);

        // Act
        boolean result = tPmsGlamsService.add(data);

        // Assert
        assertFalse(result);

        verify(numberIdGenerator).generateId(tenantId);
        verify(tPmsGlamsSelfMapper).insert(any(TPmsGlams.class));
    }

    @Test
    void testUpdate_Success() {
        // Arrange
        TPmsGlamsDTO data = new TPmsGlamsDTO();
        data.setId("1");
        data.setOrganizationNumber("0001");
        data.setBranchid("DINERS");
        data.setTxnCode("TXN001");
        data.setFinanceStatus("A");
        data.setIntInt("I");
        data.setProductNumber("PROD001");
        data.setPriceTaxInd("Y");
        data.setDrcr("D");
        data.setGlAcct("1001");
        data.setCreateTime(LocalDateTime.now());
        data.setUpdateTime(LocalDateTime.now());
        data.setUpdateBy("testUser");
        data.setVersionNumber(1L);

        Mockito.lenient().when(tPmsGlamsMapper.updateByPrimaryKeySelective(any(TPmsGlams.class))).thenReturn(1);

        // Act
        boolean result = tPmsGlamsService.update(data);

        // Assert
        assertTrue(result);

        verify(tPmsGlamsMapper).updateByPrimaryKeySelective(any(TPmsGlams.class));
    }

    @Test
    void testUpdate_NullData() {
        // Arrange
        TPmsGlamsDTO data = null;

        // Act
        boolean result = tPmsGlamsService.update(data);

        // Assert
        assertFalse(result);

        verify(tPmsGlamsMapper, never()).updateByPrimaryKeySelective(any(TPmsGlams.class));
    }

    @Test
    void testUpdate_NullId() {
        // Arrange
        TPmsGlamsDTO data = new TPmsGlamsDTO();
        data.setId(null);
        data.setOrganizationNumber("0001");
        data.setBranchid("DINERS");
        data.setTxnCode("TXN001");

        // Act
        boolean result = tPmsGlamsService.update(data);

        // Assert
        assertFalse(result);

        verify(tPmsGlamsMapper, never()).updateByPrimaryKeySelective(any(TPmsGlams.class));
    }

    @Test
    void testUpdate_UpdateFailed() {
        // Arrange
        TPmsGlamsDTO data = new TPmsGlamsDTO();
        data.setId("1");
        data.setOrganizationNumber("0001");
        data.setBranchid("DINERS");
        data.setTxnCode("TXN001");
        data.setFinanceStatus("A");
        data.setIntInt("I");
        data.setProductNumber("PROD001");
        data.setPriceTaxInd("Y");
        data.setDrcr("D");
        data.setGlAcct("1001");
        data.setCreateTime(LocalDateTime.now());
        data.setUpdateTime(LocalDateTime.now());
        data.setUpdateBy("testUser");
        data.setVersionNumber(1L);

        Mockito.lenient().when(tPmsGlamsMapper.updateByPrimaryKeySelective(any(TPmsGlams.class))).thenReturn(0);

        // Act
        boolean result = tPmsGlamsService.update(data);

        // Assert
        assertFalse(result);

        verify(tPmsGlamsMapper).updateByPrimaryKeySelective(any(TPmsGlams.class));
    }

    @org.junit.jupiter.api.AfterEach
    void tearDown() {
        if (beanMappingMockedStatic != null) {
            beanMappingMockedStatic.close();
        }
        if (orgNumberUtilsMockedStatic != null) {
            orgNumberUtilsMockedStatic.close();
        }
        if (tenantUtilsMockedStatic != null) {
            tenantUtilsMockedStatic.close();
        }
        if (pageHelperMockedStatic != null) {
            pageHelperMockedStatic.close();
        }
    }
}
